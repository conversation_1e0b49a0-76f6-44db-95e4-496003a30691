#!/usr/bin/env node

const { OCPPWebSocketServer } = require('./dist/lib/websocket-server.js');

// Server configuration
const config = {
  port: process.env.OCPP_PORT || 8080,
  heartbeatInterval: 30000, // 30 seconds
  connectionTimeout: 60000, // 60 seconds
  maxConnections: 1000
};

console.log('Starting OCPP WebSocket Server...');
console.log('Configuration:', config);

// Create and start the server
const server = new OCPPWebSocketServer(config);

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\nReceived SIGINT, shutting down gracefully...');
  await server.stop();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\nReceived SIGTERM, shutting down gracefully...');
  await server.stop();
  process.exit(0);
});

// Log server statistics every 30 seconds
setInterval(() => {
  const stats = server.getStats();
  console.log('Server Stats:', {
    ...stats,
    timestamp: new Date().toISOString()
  });
}, 30000);

console.log(`OCPP WebSocket Server is running on port ${config.port}`);
console.log('Charge points can connect to: ws://localhost:' + config.port + '/{chargePointId}');
console.log('Press Ctrl+C to stop the server');
