'use client';

interface Connection {
  id: string;
  chargePointId: string;
  isAlive: boolean;
  connectedAt: string;
  lastSeen: string;
  protocol: string;
}

interface ConnectionListProps {
  connections: Connection[];
  selectedConnection: string | null;
  onSelectConnection: (connectionId: string | null) => void;
}

export function ConnectionList({ connections, selectedConnection, onSelectConnection }: ConnectionListProps) {
  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const getTimeSince = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffMs = now.getTime() - time.getTime();
    const diffSecs = Math.floor(diffMs / 1000);
    const diffMins = Math.floor(diffSecs / 60);
    const diffHours = Math.floor(diffMins / 60);

    if (diffSecs < 60) return `${diffSecs}s ago`;
    if (diffMins < 60) return `${diffMins}m ago`;
    return `${diffHours}h ago`;
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            Connections ({connections.length})
          </h2>
          <button
            onClick={() => onSelectConnection(null)}
            className={`px-3 py-1 text-sm rounded-md transition-colors ${
              selectedConnection === null
                ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'
            }`}
          >
            All
          </button>
        </div>
      </div>

      <div className="max-h-96 overflow-y-auto">
        {connections.length === 0 ? (
          <div className="p-4 text-center text-gray-500 dark:text-gray-400">
            No connections
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {connections.map((connection) => (
              <div
                key={connection.id}
                onClick={() => onSelectConnection(connection.id)}
                className={`p-4 cursor-pointer transition-colors ${
                  selectedConnection === connection.id
                    ? 'bg-blue-50 dark:bg-blue-900/20 border-r-2 border-blue-500'
                    : 'hover:bg-gray-50 dark:hover:bg-gray-700'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-3 h-3 rounded-full ${
                      connection.isAlive ? 'bg-green-500' : 'bg-red-500'
                    }`}></div>
                    <div>
                      <div className="font-medium text-gray-900 dark:text-white">
                        {connection.chargePointId}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {connection.protocol}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Connected: {formatTime(connection.connectedAt)}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Last seen: {getTimeSince(connection.lastSeen)}
                    </div>
                  </div>
                </div>
                
                <div className="mt-2 text-xs text-gray-400 dark:text-gray-500 font-mono">
                  ID: {connection.id.substring(0, 8)}...
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
