'use client';

import { useState, useEffect, useRef } from 'react';
import { ConnectionList } from './ConnectionList';
import { MessageLog } from './MessageLog';
import { ServerStats } from './ServerStats';

interface Connection {
  id: string;
  chargePointId: string;
  isAlive: boolean;
  connectedAt: string;
  lastSeen: string;
  protocol: string;
}

interface Message {
  id: string;
  timestamp: string;
  connectionId: string;
  chargePointId: string;
  direction: 'incoming' | 'outgoing';
  messageType: string;
  action?: string;
  payload: any;
}

interface Stats {
  totalConnections: number;
  activeConnections: number;
  connectionsByProtocol: Record<string, number>;
}

export function OCPPDashboard() {
  const [connections, setConnections] = useState<Connection[]>([]);
  const [messages, setMessages] = useState<Message[]>([]);
  const [stats, setStats] = useState<Stats>({
    totalConnections: 0,
    activeConnections: 0,
    connectionsByProtocol: {}
  });
  const [selectedConnection, setSelectedConnection] = useState<string | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [serverRunning, setServerRunning] = useState(false);
  const wsRef = useRef<WebSocket | null>(null);

  useEffect(() => {
    // Check if server is running
    checkServerStatus();
    
    // Try to connect to management WebSocket
    connectToManagement();

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, []);

  const checkServerStatus = async () => {
    try {
      const response = await fetch('/api/websocket');
      if (response.ok) {
        setServerRunning(true);
      }
    } catch (error) {
      setServerRunning(false);
    }
  };

  const connectToManagement = () => {
    try {
      const ws = new WebSocket('ws://localhost:8080/management');
      wsRef.current = ws;

      ws.onopen = () => {
        console.log('Connected to management WebSocket');
        setIsConnected(true);
      };

      ws.onclose = () => {
        console.log('Disconnected from management WebSocket');
        setIsConnected(false);
        // Try to reconnect after 5 seconds
        setTimeout(connectToManagement, 5000);
      };

      ws.onerror = (error) => {
        console.error('Management WebSocket error:', error);
        setIsConnected(false);
      };

      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          handleManagementMessage(data);
        } catch (error) {
          console.error('Error parsing management message:', error);
        }
      };
    } catch (error) {
      console.error('Failed to connect to management WebSocket:', error);
      setIsConnected(false);
      // Try to reconnect after 5 seconds
      setTimeout(connectToManagement, 5000);
    }
  };

  const handleManagementMessage = (data: any) => {
    switch (data.type) {
      case 'initial':
        setConnections(data.connections || []);
        setMessages(data.messages || []);
        setStats(data.stats || { totalConnections: 0, activeConnections: 0, connectionsByProtocol: {} });
        break;
      case 'update':
        setConnections(data.connections || []);
        setStats(data.stats || { totalConnections: 0, activeConnections: 0, connectionsByProtocol: {} });
        break;
      case 'newMessage':
        setMessages(prev => [...prev, data.message].slice(-1000)); // Keep last 1000 messages
        break;
      case 'connections':
        setConnections(data.connections || []);
        break;
      case 'messages':
        setMessages(data.messages || []);
        break;
    }
  };

  const clearMessages = () => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({ type: 'clearMessages' }));
    }
    setMessages([]);
  };

  const refreshConnections = () => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({ type: 'getConnections' }));
    }
  };

  const filteredMessages = selectedConnection 
    ? messages.filter(msg => msg.connectionId === selectedConnection)
    : messages;

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                OCPP Backend - Dev UI
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Real-time monitoring of WebSocket connections and OCPP messages
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {isConnected ? 'Connected' : 'Disconnected'}
                </span>
              </div>
              <button
                onClick={refreshConnections}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                disabled={!isConnected}
              >
                Refresh
              </button>
            </div>
          </div>
        </div>

        {!isConnected && (
          <div className="mb-6 p-4 bg-yellow-100 dark:bg-yellow-900 border border-yellow-300 dark:border-yellow-700 rounded-lg">
            <div className="flex items-center">
              <div className="text-yellow-800 dark:text-yellow-200">
                <strong>Not connected to OCPP server.</strong> Make sure the server is running on port 8080.
                <br />
                <code className="text-sm bg-yellow-200 dark:bg-yellow-800 px-2 py-1 rounded mt-2 inline-block">
                  npm run server
                </code>
              </div>
            </div>
          </div>
        )}

        {/* Stats */}
        <div className="mb-6">
          <ServerStats stats={stats} />
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Connections */}
          <div className="lg:col-span-1">
            <ConnectionList 
              connections={connections}
              selectedConnection={selectedConnection}
              onSelectConnection={setSelectedConnection}
            />
          </div>

          {/* Messages */}
          <div className="lg:col-span-2">
            <MessageLog 
              messages={filteredMessages}
              selectedConnection={selectedConnection}
              onClearMessages={clearMessages}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
