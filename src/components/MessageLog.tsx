'use client';

import { useState, useEffect, useRef } from 'react';

interface Message {
  id: string;
  timestamp: string;
  connectionId: string;
  chargePointId: string;
  direction: 'incoming' | 'outgoing';
  messageType: string;
  action?: string;
  payload: any;
}

interface MessageLogProps {
  messages: Message[];
  selectedConnection: string | null;
  onClearMessages: () => void;
}

export function MessageLog({ messages, selectedConnection, onClearMessages }: MessageLogProps) {
  const [autoScroll, setAutoScroll] = useState(true);
  const [expandedMessage, setExpandedMessage] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'formatted' | 'raw'>('formatted');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (autoScroll) {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, autoScroll]);

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('en-US', { 
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      fractionalSecondDigits: 3
    });
  };

  const getMessageTypeColor = (messageType: string, direction: string) => {
    if (direction === 'incoming') {
      switch (messageType) {
        case 'CALL': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
        case 'CALLRESULT': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
        case 'CALLERROR': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
        default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      }
    } else {
      switch (messageType) {
        case 'CALLRESULT': return 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200';
        case 'CALLERROR': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
        default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      }
    }
  };

  const getDirectionIcon = (direction: string) => {
    return direction === 'incoming' ? '→' : '←';
  };

  const toggleExpanded = (messageId: string) => {
    setExpandedMessage(expandedMessage === messageId ? null : messageId);
  };

  const formatPayload = (payload: any) => {
    try {
      return JSON.stringify(payload, null, 2);
    } catch {
      return String(payload);
    }
  };

  const formatRawMessage = (message: Message) => {
    // Show the raw OCPP message as it would appear on the wire
    return JSON.stringify(message.payload, null, 2);
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            Message Log ({messages.length})
            {selectedConnection && (
              <span className="ml-2 text-sm font-normal text-gray-500 dark:text-gray-400">
                - Filtered by connection
              </span>
            )}
          </h2>
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
              <button
                onClick={() => setViewMode('formatted')}
                className={`px-3 py-1 text-sm rounded-md transition-colors ${
                  viewMode === 'formatted'
                    ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                Formatted
              </button>
              <button
                onClick={() => setViewMode('raw')}
                className={`px-3 py-1 text-sm rounded-md transition-colors ${
                  viewMode === 'raw'
                    ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                Raw JSON
              </button>
            </div>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={autoScroll}
                onChange={(e) => setAutoScroll(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-600 dark:text-gray-400">Auto-scroll</span>
            </label>
            <button
              onClick={onClearMessages}
              className="px-3 py-1 text-sm bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
            >
              Clear
            </button>
          </div>
        </div>
      </div>

      <div className="h-96 overflow-y-auto">
        {messages.length === 0 ? (
          <div className="p-4 text-center text-gray-500 dark:text-gray-400">
            No messages
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {messages.map((message) => (
              <div key={message.id} className="p-3 hover:bg-gray-50 dark:hover:bg-gray-700">
                {viewMode === 'formatted' ? (
                  // Formatted View
                  <>
                    <div
                      className="flex items-center justify-between cursor-pointer"
                      onClick={() => toggleExpanded(message.id)}
                    >
                      <div className="flex items-center space-x-3">
                        <span className="text-xs font-mono text-gray-500 dark:text-gray-400">
                          {formatTime(message.timestamp)}
                        </span>
                        <span className="text-lg">
                          {getDirectionIcon(message.direction)}
                        </span>
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                          getMessageTypeColor(message.messageType, message.direction)
                        }`}>
                          {message.messageType}
                        </span>
                        {message.action && (
                          <span className="px-2 py-1 text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200 rounded-full">
                            {message.action}
                          </span>
                        )}
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {message.chargePointId}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-xs text-gray-400 dark:text-gray-500 font-mono">
                          {message.connectionId.substring(0, 8)}...
                        </span>
                        <span className="text-gray-400 dark:text-gray-500">
                          {expandedMessage === message.id ? '▼' : '▶'}
                        </span>
                      </div>
                    </div>

                    {expandedMessage === message.id && (
                      <div className="mt-3 p-3 bg-gray-50 dark:bg-gray-900 rounded-md">
                        <div className="text-xs text-gray-600 dark:text-gray-400 mb-2">
                          <strong>Connection ID:</strong> {message.connectionId}
                        </div>
                        <div className="flex items-center justify-between mb-2">
                          <div className="text-xs text-gray-600 dark:text-gray-400">
                            <strong>Payload:</strong>
                          </div>
                          <button
                            onClick={() => copyToClipboard(formatPayload(message.payload))}
                            className="px-2 py-1 text-xs bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
                          >
                            Copy
                          </button>
                        </div>
                        <pre className="text-xs bg-white dark:bg-gray-800 p-2 rounded border overflow-x-auto">
                          <code>{formatPayload(message.payload)}</code>
                        </pre>
                      </div>
                    )}
                  </>
                ) : (
                  // Raw JSON View
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <span className="text-xs font-mono text-gray-500 dark:text-gray-400">
                          {formatTime(message.timestamp)}
                        </span>
                        <span className="text-lg">
                          {getDirectionIcon(message.direction)}
                        </span>
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {message.chargePointId}
                        </span>
                        <span className="text-xs text-gray-400 dark:text-gray-500 font-mono">
                          {message.connectionId.substring(0, 8)}...
                        </span>
                      </div>
                      <button
                        onClick={() => copyToClipboard(formatRawMessage(message))}
                        className="px-2 py-1 text-xs bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
                      >
                        Copy JSON
                      </button>
                    </div>
                    <pre className="text-xs bg-gray-50 dark:bg-gray-900 p-3 rounded border overflow-x-auto">
                      <code className="text-gray-800 dark:text-gray-200">{formatRawMessage(message)}</code>
                    </pre>
                  </div>
                )}
              </div>
            ))}
            <div ref={messagesEndRef} />
          </div>
        )}
      </div>
    </div>
  );
}
