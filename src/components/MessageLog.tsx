'use client';

import { useState, useEffect, useRef } from 'react';

interface Message {
  id: string;
  timestamp: string;
  connectionId: string;
  chargePointId: string;
  direction: 'incoming' | 'outgoing';
  messageType: string;
  action?: string;
  payload: any;
}

interface MessageLogProps {
  messages: Message[];
  selectedConnection: string | null;
  onClearMessages: () => void;
}

export function MessageLog({ messages, selectedConnection, onClearMessages }: MessageLogProps) {
  const [autoScroll, setAutoScroll] = useState(true);
  const [expandedMessage, setExpandedMessage] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (autoScroll) {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, autoScroll]);

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('en-US', { 
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      fractionalSecondDigits: 3
    });
  };

  const getMessageTypeColor = (messageType: string, direction: string) => {
    if (direction === 'incoming') {
      switch (messageType) {
        case 'CALL': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
        case 'CALLRESULT': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
        case 'CALLERROR': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
        default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      }
    } else {
      switch (messageType) {
        case 'CALLRESULT': return 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200';
        case 'CALLERROR': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
        default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      }
    }
  };

  const getDirectionIcon = (direction: string) => {
    return direction === 'incoming' ? '→' : '←';
  };

  const toggleExpanded = (messageId: string) => {
    setExpandedMessage(expandedMessage === messageId ? null : messageId);
  };

  const formatPayload = (payload: any) => {
    try {
      return JSON.stringify(payload, null, 2);
    } catch {
      return String(payload);
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            Message Log ({messages.length})
            {selectedConnection && (
              <span className="ml-2 text-sm font-normal text-gray-500 dark:text-gray-400">
                - Filtered by connection
              </span>
            )}
          </h2>
          <div className="flex items-center space-x-3">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={autoScroll}
                onChange={(e) => setAutoScroll(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-600 dark:text-gray-400">Auto-scroll</span>
            </label>
            <button
              onClick={onClearMessages}
              className="px-3 py-1 text-sm bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
            >
              Clear
            </button>
          </div>
        </div>
      </div>

      <div className="h-96 overflow-y-auto">
        {messages.length === 0 ? (
          <div className="p-4 text-center text-gray-500 dark:text-gray-400">
            No messages
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {messages.map((message) => (
              <div key={message.id} className="p-3 hover:bg-gray-50 dark:hover:bg-gray-700">
                <div 
                  className="flex items-center justify-between cursor-pointer"
                  onClick={() => toggleExpanded(message.id)}
                >
                  <div className="flex items-center space-x-3">
                    <span className="text-xs font-mono text-gray-500 dark:text-gray-400">
                      {formatTime(message.timestamp)}
                    </span>
                    <span className="text-lg">
                      {getDirectionIcon(message.direction)}
                    </span>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      getMessageTypeColor(message.messageType, message.direction)
                    }`}>
                      {message.messageType}
                    </span>
                    {message.action && (
                      <span className="px-2 py-1 text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200 rounded-full">
                        {message.action}
                      </span>
                    )}
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {message.chargePointId}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-xs text-gray-400 dark:text-gray-500 font-mono">
                      {message.connectionId.substring(0, 8)}...
                    </span>
                    <span className="text-gray-400 dark:text-gray-500">
                      {expandedMessage === message.id ? '▼' : '▶'}
                    </span>
                  </div>
                </div>
                
                {expandedMessage === message.id && (
                  <div className="mt-3 p-3 bg-gray-50 dark:bg-gray-900 rounded-md">
                    <div className="text-xs text-gray-600 dark:text-gray-400 mb-2">
                      <strong>Connection ID:</strong> {message.connectionId}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400 mb-2">
                      <strong>Payload:</strong>
                    </div>
                    <pre className="text-xs bg-white dark:bg-gray-800 p-2 rounded border overflow-x-auto">
                      <code>{formatPayload(message.payload)}</code>
                    </pre>
                  </div>
                )}
              </div>
            ))}
            <div ref={messagesEndRef} />
          </div>
        )}
      </div>
    </div>
  );
}
