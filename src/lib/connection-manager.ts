import { v4 as uuidv4 } from 'uuid';
import { WebSocket } from 'ws';
import { OCPPConnection, ConnectionStats, ConnectionEvent, ConnectionEventType } from './types/websocket';
import { OCPPMessageUnion } from './types/ocpp';

export class ConnectionManager {
  private connections: Map<string, OCPPConnection> = new Map();
  private chargePointConnections: Map<string, string> = new Map(); // chargePointId -> connectionId
  private eventListeners: Map<ConnectionEventType, Array<(event: ConnectionEvent) => void>> = new Map();

  constructor() {
    // Initialize event listeners map
    Object.values(['connect', 'disconnect', 'message', 'error', 'heartbeat'] as ConnectionEventType[])
      .forEach(type => this.eventListeners.set(type, []));
  }

  /**
   * Add a new connection
   */
  addConnection(socket: WebSocket, chargePointId: string, protocol?: string): OCPPConnection {
    const connectionId = uuidv4();
    const now = new Date();
    
    const connection: OCPPConnection = {
      id: connectionId,
      chargePointId,
      socket,
      isAlive: true,
      connectedAt: now,
      lastSeen: now,
      protocol
    };

    // Remove existing connection for this charge point if any
    const existingConnectionId = this.chargePointConnections.get(chargePointId);
    if (existingConnectionId) {
      this.removeConnection(existingConnectionId);
    }

    this.connections.set(connectionId, connection);
    this.chargePointConnections.set(chargePointId, connectionId);

    this.emitEvent({
      type: 'connect',
      connectionId,
      chargePointId,
      timestamp: now
    });

    console.log(`New connection established: ${chargePointId} (${connectionId})`);
    return connection;
  }

  /**
   * Remove a connection
   */
  removeConnection(connectionId: string): boolean {
    const connection = this.connections.get(connectionId);
    if (!connection) {
      return false;
    }

    this.connections.delete(connectionId);
    this.chargePointConnections.delete(connection.chargePointId);

    this.emitEvent({
      type: 'disconnect',
      connectionId,
      chargePointId: connection.chargePointId,
      timestamp: new Date()
    });

    console.log(`Connection removed: ${connection.chargePointId} (${connectionId})`);
    return true;
  }

  /**
   * Get connection by ID
   */
  getConnection(connectionId: string): OCPPConnection | undefined {
    return this.connections.get(connectionId);
  }

  /**
   * Get connection by charge point ID
   */
  getConnectionByChargePoint(chargePointId: string): OCPPConnection | undefined {
    const connectionId = this.chargePointConnections.get(chargePointId);
    return connectionId ? this.connections.get(connectionId) : undefined;
  }

  /**
   * Get all connections
   */
  getAllConnections(): OCPPConnection[] {
    return Array.from(this.connections.values());
  }

  /**
   * Update connection's last seen timestamp
   */
  updateLastSeen(connectionId: string): void {
    const connection = this.connections.get(connectionId);
    if (connection) {
      connection.lastSeen = new Date();
      connection.isAlive = true;
    }
  }

  /**
   * Mark connection as alive (for heartbeat)
   */
  markAlive(connectionId: string): void {
    const connection = this.connections.get(connectionId);
    if (connection) {
      connection.isAlive = true;
      connection.lastSeen = new Date();
      
      this.emitEvent({
        type: 'heartbeat',
        connectionId,
        chargePointId: connection.chargePointId,
        timestamp: new Date()
      });
    }
  }

  /**
   * Mark connection as not alive
   */
  markNotAlive(connectionId: string): void {
    const connection = this.connections.get(connectionId);
    if (connection) {
      connection.isAlive = false;
    }
  }

  /**
   * Get connection statistics
   */
  getStats(): ConnectionStats {
    const connections = Array.from(this.connections.values());
    const protocolCounts: Record<string, number> = {};

    connections.forEach(conn => {
      const protocol = conn.protocol || 'unknown';
      protocolCounts[protocol] = (protocolCounts[protocol] || 0) + 1;
    });

    return {
      totalConnections: connections.length,
      activeConnections: connections.filter(conn => conn.isAlive).length,
      connectionsByProtocol: protocolCounts
    };
  }

  /**
   * Send message to a specific charge point
   */
  sendToChargePoint(chargePointId: string, message: OCPPMessageUnion): boolean {
    const connection = this.getConnectionByChargePoint(chargePointId);
    if (!connection || connection.socket.readyState !== WebSocket.OPEN) {
      return false;
    }

    try {
      connection.socket.send(JSON.stringify(message));
      this.updateLastSeen(connection.id);
      return true;
    } catch (error) {
      console.error(`Failed to send message to ${chargePointId}:`, error);
      return false;
    }
  }

  /**
   * Broadcast message to all connected charge points
   */
  broadcast(message: OCPPMessageUnion): number {
    let sentCount = 0;
    
    this.connections.forEach(connection => {
      if (connection.socket.readyState === WebSocket.OPEN) {
        try {
          connection.socket.send(JSON.stringify(message));
          this.updateLastSeen(connection.id);
          sentCount++;
        } catch (error) {
          console.error(`Failed to broadcast to ${connection.chargePointId}:`, error);
        }
      }
    });

    return sentCount;
  }

  /**
   * Add event listener
   */
  on(eventType: ConnectionEventType, listener: (event: ConnectionEvent) => void): void {
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      listeners.push(listener);
    }
  }

  /**
   * Remove event listener
   */
  off(eventType: ConnectionEventType, listener: (event: ConnectionEvent) => void): void {
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * Emit event to all listeners
   */
  private emitEvent(event: ConnectionEvent): void {
    const listeners = this.eventListeners.get(event.type);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(event);
        } catch (error) {
          console.error(`Error in event listener for ${event.type}:`, error);
        }
      });
    }
  }

  /**
   * Clean up dead connections
   */
  cleanup(): number {
    let removedCount = 0;
    const now = new Date();
    const timeout = 60000; // 60 seconds timeout

    this.connections.forEach((connection, connectionId) => {
      if (!connection.isAlive || 
          (now.getTime() - connection.lastSeen.getTime()) > timeout ||
          connection.socket.readyState === WebSocket.CLOSED ||
          connection.socket.readyState === WebSocket.CLOSING) {
        this.removeConnection(connectionId);
        removedCount++;
      }
    });

    return removedCount;
  }
}
