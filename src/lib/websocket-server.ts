import { WebSocketServer, WebSocket } from 'ws';
import { IncomingMessage } from 'http';
import { ConnectionManager } from './connection-manager';
import { ServerConfig, OCPPConnection } from './types/websocket';
import {
  OCPPMessageUnion,
  OCPPMessageType,
  OCPPErrorCode,
  OCPPMessageArray,
  OCPPAction,
  BootNotificationRequest,
  BootNotificationResponse,
  HeartbeatRequest,
  HeartbeatResponse,
  StatusNotificationRequest,
  MeterValuesRequest,
  StartTransactionRequest,
  StopTransactionRequest
} from './types/ocpp';

export class OCPPWebSocketServer {
  private wss: WebSocketServer;
  private connectionManager: ConnectionManager;
  private config: ServerConfig;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private cleanupInterval: NodeJS.Timeout | null = null;
  private managementClients: Set<WebSocket> = new Set();
  private messageLog: Array<{
    id: string;
    timestamp: Date;
    connectionId: string;
    chargePointId: string;
    direction: 'incoming' | 'outgoing';
    messageType: string;
    action?: string;
    payload: any;
  }> = [];

  constructor(config: Partial<ServerConfig> = {}) {
    this.config = {
      port: config.port || 8080,
      heartbeatInterval: config.heartbeatInterval || 30000, // 30 seconds
      connectionTimeout: config.connectionTimeout || 60000, // 60 seconds
      maxConnections: config.maxConnections || 1000
    };

    this.connectionManager = new ConnectionManager();
    this.wss = new WebSocketServer({
      port: this.config.port,
      verifyClient: this.verifyClient.bind(this)
    });

    this.setupEventHandlers();
    this.startHeartbeat();
    this.startCleanup();

    console.log(`OCPP WebSocket Server started on port ${this.config.port}`);
  }

  /**
   * Verify client connection (extract charge point ID from URL or handle management connections)
   */
  private verifyClient(info: { origin: string; secure: boolean; req: IncomingMessage }): boolean {
    const url = new URL(info.req.url || '', `http://${info.req.headers.host}`);
    const pathname = url.pathname;

    // Allow management UI connections
    if (pathname === '/management') {
      return true;
    }

    const chargePointId = pathname.split('/').pop();

    if (!chargePointId || chargePointId.length === 0) {
      console.log('Connection rejected: No charge point ID in URL');
      return false;
    }

    // Check max connections
    if (this.connectionManager.getAllConnections().length >= this.config.maxConnections) {
      console.log(`Connection rejected: Max connections (${this.config.maxConnections}) reached`);
      return false;
    }

    return true;
  }

  /**
   * Setup WebSocket server event handlers
   */
  private setupEventHandlers(): void {
    this.wss.on('connection', (socket: WebSocket, request: IncomingMessage) => {
      this.handleConnection(socket, request);
    });

    this.wss.on('error', (error: Error) => {
      console.error('WebSocket Server Error:', error);
    });

    // Setup connection manager event listeners
    this.connectionManager.on('connect', (event) => {
      console.log(`Charge Point Connected: ${event.chargePointId} at ${event.timestamp.toISOString()}`);
    });

    this.connectionManager.on('disconnect', (event) => {
      console.log(`Charge Point Disconnected: ${event.chargePointId} at ${event.timestamp.toISOString()}`);
    });

    this.connectionManager.on('message', (event) => {
      console.log(`Message from ${event.chargePointId}:`, event.data);
    });

    this.connectionManager.on('error', (event) => {
      console.error(`Error from ${event.chargePointId}:`, event.data);
    });
  }

  /**
   * Handle new WebSocket connection
   */
  private handleConnection(socket: WebSocket, request: IncomingMessage): void {
    const url = new URL(request.url || '', `http://${request.headers.host}`);
    const pathname = url.pathname;

    // Handle management UI connections
    if (pathname === '/management') {
      this.handleManagementConnection(socket);
      return;
    }

    const chargePointId = pathname.split('/').pop() || 'unknown';
    const protocol = socket.protocol;

    // Add connection to manager
    const connection = this.connectionManager.addConnection(socket, chargePointId, protocol);

    // Setup socket event handlers
    socket.on('message', (data: Buffer) => {
      this.handleMessage(connection, data);
    });

    socket.on('close', (code: number, reason: Buffer) => {
      console.log(`Connection closed: ${chargePointId} (${code}: ${reason.toString()})`);
      this.connectionManager.removeConnection(connection.id);
      this.broadcastToManagement();
    });

    socket.on('error', (error: Error) => {
      console.error(`Socket error for ${chargePointId}:`, error);
      this.connectionManager.removeConnection(connection.id);
      this.broadcastToManagement();
    });

    socket.on('pong', () => {
      this.connectionManager.markAlive(connection.id);
    });

    // Send initial ping
    this.pingConnection(connection);

    // Notify management clients
    this.broadcastToManagement();
  }

  /**
   * Handle management UI connection
   */
  private handleManagementConnection(socket: WebSocket): void {
    console.log('Management UI connected');
    this.managementClients.add(socket);

    socket.on('close', () => {
      console.log('Management UI disconnected');
      this.managementClients.delete(socket);
    });

    socket.on('error', (error: Error) => {
      console.error('Management UI error:', error);
      this.managementClients.delete(socket);
    });

    socket.on('message', (data: Buffer) => {
      try {
        const message = JSON.parse(data.toString());
        this.handleManagementMessage(socket, message);
      } catch (error) {
        console.error('Invalid management message:', error);
      }
    });

    // Send initial data
    this.sendToManagementClient(socket, {
      type: 'initial',
      connections: this.getConnectionsForUI(),
      messages: this.messageLog.slice(-100), // Last 100 messages
      stats: this.getStats()
    });
  }

  /**
   * Handle management UI messages
   */
  private handleManagementMessage(socket: WebSocket, message: any): void {
    switch (message.type) {
      case 'getConnections':
        this.sendToManagementClient(socket, {
          type: 'connections',
          connections: this.getConnectionsForUI()
        });
        break;
      case 'getMessages':
        const { connectionId, limit = 100 } = message;
        let messages = this.messageLog.slice(-limit);
        if (connectionId) {
          messages = messages.filter(msg => msg.connectionId === connectionId);
        }
        this.sendToManagementClient(socket, {
          type: 'messages',
          messages
        });
        break;
      case 'clearMessages':
        this.messageLog = [];
        this.broadcastToManagement();
        break;
    }
  }

  /**
   * Send message to specific management client
   */
  private sendToManagementClient(socket: WebSocket, data: any): void {
    if (socket.readyState === WebSocket.OPEN) {
      socket.send(JSON.stringify(data));
    }
  }

  /**
   * Broadcast to all management clients
   */
  private broadcastToManagement(): void {
    const data = {
      type: 'update',
      connections: this.getConnectionsForUI(),
      stats: this.getStats(),
      timestamp: new Date().toISOString()
    };

    this.managementClients.forEach(client => {
      this.sendToManagementClient(client, data);
    });
  }

  /**
   * Get connections formatted for UI
   */
  private getConnectionsForUI() {
    return this.connectionManager.getAllConnections().map(conn => ({
      id: conn.id,
      chargePointId: conn.chargePointId,
      isAlive: conn.isAlive,
      connectedAt: conn.connectedAt.toISOString(),
      lastSeen: conn.lastSeen.toISOString(),
      protocol: conn.protocol || 'unknown'
    }));
  }

  /**
   * Log message for UI
   */
  private logMessage(connectionId: string, chargePointId: string, direction: 'incoming' | 'outgoing', messageType: string, action: string | undefined, payload: any): void {
    const logEntry = {
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      connectionId,
      chargePointId,
      direction,
      messageType,
      action,
      payload
    };

    this.messageLog.push(logEntry);

    // Keep only last 1000 messages
    if (this.messageLog.length > 1000) {
      this.messageLog = this.messageLog.slice(-1000);
    }

    // Broadcast new message to management clients
    this.managementClients.forEach(client => {
      this.sendToManagementClient(client, {
        type: 'newMessage',
        message: {
          ...logEntry,
          timestamp: logEntry.timestamp.toISOString()
        }
      });
    });
  }

  /**
   * Handle incoming message from charge point
   */
  private handleMessage(connection: OCPPConnection, data: Buffer): void {
    try {
      const messageStr = data.toString();
      const message: OCPPMessageArray = JSON.parse(messageStr);

      // Validate message format
      if (!Array.isArray(message) || message.length < 3) {
        this.sendError(connection, '', OCPPErrorCode.FORMATION_VIOLATION, 'Invalid message format');
        return;
      }

      const [messageTypeId, messageId] = message;

      // Update connection activity
      this.connectionManager.updateLastSeen(connection.id);

      // Log message for UI
      const action = messageTypeId === OCPPMessageType.CALL ? message[2] : undefined;
      const messageTypeName = messageTypeId === OCPPMessageType.CALL ? 'CALL' :
                             messageTypeId === OCPPMessageType.CALLRESULT ? 'CALLRESULT' : 'CALLERROR';
      this.logMessage(connection.id, connection.chargePointId, 'incoming', messageTypeName, action, message);

      // Emit message event
      this.connectionManager['emitEvent']({
        type: 'message',
        connectionId: connection.id,
        chargePointId: connection.chargePointId,
        data: message,
        timestamp: new Date()
      });

      // Handle different message types
      switch (messageTypeId) {
        case OCPPMessageType.CALL:
          this.handleCall(connection, message as [number, string, string, any]);
          break;
        case OCPPMessageType.CALLRESULT:
          this.handleCallResult(connection, message as [number, string, any]);
          break;
        case OCPPMessageType.CALLERROR:
          this.handleCallError(connection, message as [number, string, string, string, any]);
          break;
        default:
          this.sendError(connection, messageId, OCPPErrorCode.NOT_SUPPORTED, 'Unsupported message type');
      }
    } catch (error) {
      console.error(`Error parsing message from ${connection.chargePointId}:`, error);
      this.sendError(connection, '', OCPPErrorCode.FORMATION_VIOLATION, 'Invalid JSON');
    }
  }

  /**
   * Handle CALL message (request from charge point)
   */
  private handleCall(connection: OCPPConnection, message: [number, string, string, any]): void {
    const [, messageId, action, payload] = message;

    console.log(`Received ${action} from ${connection.chargePointId}:`, payload);

    // Handle actions with proper typing
    switch (action) {
      case OCPPAction.BOOT_NOTIFICATION:
        this.handleBootNotification(connection, messageId, payload as BootNotificationRequest);
        break;
      case OCPPAction.HEARTBEAT:
        this.handleHeartbeat(connection, messageId, payload as HeartbeatRequest);
        break;
      case OCPPAction.STATUS_NOTIFICATION:
        this.handleStatusNotification(connection, messageId, payload as StatusNotificationRequest);
        break;
      case OCPPAction.METER_VALUES:
        this.handleMeterValues(connection, messageId, payload as MeterValuesRequest);
        break;
      case OCPPAction.START_TRANSACTION:
        this.handleStartTransaction(connection, messageId, payload as StartTransactionRequest);
        break;
      case OCPPAction.STOP_TRANSACTION:
        this.handleStopTransaction(connection, messageId, payload as StopTransactionRequest);
        break;
      default:
        this.sendError(connection, messageId, OCPPErrorCode.NOT_IMPLEMENTED, `Action ${action} not implemented`);
    }
  }

  /**
   * Handle BootNotification request
   */
  private handleBootNotification(connection: OCPPConnection, messageId: string, request: BootNotificationRequest): void {
    // Validate required fields
    if (!request.chargePointVendor || !request.chargePointModel) {
      this.sendError(connection, messageId, OCPPErrorCode.PROPERTY_CONSTRAINT_VIOLATION, 'Missing required fields');
      return;
    }

    const response: BootNotificationResponse = {
      status: 'Accepted',
      currentTime: new Date().toISOString(),
      interval: 300 // 5 minutes heartbeat interval
    };

    this.sendCallResult(connection, messageId, response);
  }

  /**
   * Handle Heartbeat request
   */
  private handleHeartbeat(connection: OCPPConnection, messageId: string, request: HeartbeatRequest): void {
    const response: HeartbeatResponse = {
      currentTime: new Date().toISOString()
    };

    this.sendCallResult(connection, messageId, response);
  }

  /**
   * Handle StatusNotification request
   */
  private handleStatusNotification(connection: OCPPConnection, messageId: string, request: StatusNotificationRequest): void {
    // Validate required fields
    if (request.connectorId === undefined || !request.errorCode || !request.status) {
      this.sendError(connection, messageId, OCPPErrorCode.PROPERTY_CONSTRAINT_VIOLATION, 'Missing required fields');
      return;
    }

    // Log status change
    console.log(`Status update for ${connection.chargePointId} connector ${request.connectorId}: ${request.status} (${request.errorCode})`);

    // Send empty response (StatusNotificationResponse is empty)
    this.sendCallResult(connection, messageId, {});
  }

  /**
   * Handle MeterValues request
   */
  private handleMeterValues(connection: OCPPConnection, messageId: string, request: MeterValuesRequest): void {
    // Validate required fields
    if (request.connectorId === undefined || !request.meterValue) {
      this.sendError(connection, messageId, OCPPErrorCode.PROPERTY_CONSTRAINT_VIOLATION, 'Missing required fields');
      return;
    }

    // Log meter values
    console.log(`Meter values for ${connection.chargePointId} connector ${request.connectorId}:`, request.meterValue);

    // Send empty response (MeterValuesResponse is empty)
    this.sendCallResult(connection, messageId, {});
  }

  /**
   * Handle StartTransaction request
   */
  private handleStartTransaction(connection: OCPPConnection, messageId: string, request: StartTransactionRequest): void {
    // Validate required fields
    if (request.connectorId === undefined || !request.idTag || request.meterStart === undefined || !request.timestamp) {
      this.sendError(connection, messageId, OCPPErrorCode.PROPERTY_CONSTRAINT_VIOLATION, 'Missing required fields');
      return;
    }

    // Generate a transaction ID (in real implementation, this would be stored in database)
    const transactionId = Math.floor(Math.random() * 1000000);

    const response = {
      idTagInfo: {
        status: 'Accepted' as const
      },
      transactionId
    };

    console.log(`Transaction started for ${connection.chargePointId}: ${transactionId}`);
    this.sendCallResult(connection, messageId, response);
  }

  /**
   * Handle StopTransaction request
   */
  private handleStopTransaction(connection: OCPPConnection, messageId: string, request: StopTransactionRequest): void {
    // Validate required fields
    if (request.transactionId === undefined || !request.timestamp || request.meterStop === undefined) {
      this.sendError(connection, messageId, OCPPErrorCode.PROPERTY_CONSTRAINT_VIOLATION, 'Missing required fields');
      return;
    }

    const response = {
      idTagInfo: {
        status: 'Accepted' as const
      }
    };

    console.log(`Transaction stopped for ${connection.chargePointId}: ${request.transactionId}`);
    this.sendCallResult(connection, messageId, response);
  }

  /**
   * Handle CALLRESULT message (response from charge point)
   */
  private handleCallResult(connection: OCPPConnection, message: [number, string, any]): void {
    const [, messageId, payload] = message;
    console.log(`Received CALLRESULT from ${connection.chargePointId} for message ${messageId}:`, payload);
  }

  /**
   * Handle CALLERROR message (error from charge point)
   */
  private handleCallError(connection: OCPPConnection, message: [number, string, string, string, any]): void {
    const [, messageId, errorCode, errorDescription, errorDetails] = message;
    console.error(`Received CALLERROR from ${connection.chargePointId} for message ${messageId}:`, {
      errorCode,
      errorDescription,
      errorDetails
    });
  }

  /**
   * Send CALLRESULT message to charge point
   */
  private sendCallResult(connection: OCPPConnection, messageId: string, payload: any): void {
    const message: [number, string, any] = [OCPPMessageType.CALLRESULT, messageId, payload];
    this.sendMessage(connection, message);
  }

  /**
   * Send CALLERROR message to charge point
   */
  private sendError(connection: OCPPConnection, messageId: string, errorCode: string, errorDescription: string, errorDetails?: any): void {
    const message: [number, string, string, string, any] = [
      OCPPMessageType.CALLERROR,
      messageId,
      errorCode,
      errorDescription,
      errorDetails || {}
    ];
    this.sendMessage(connection, message);
  }

  /**
   * Send message to charge point
   */
  private sendMessage(connection: OCPPConnection, message: any): void {
    if (connection.socket.readyState === WebSocket.OPEN) {
      try {
        connection.socket.send(JSON.stringify(message));
        this.connectionManager.updateLastSeen(connection.id);

        // Log outgoing message
        const messageTypeId = message[0];
        const messageTypeName = messageTypeId === OCPPMessageType.CALLRESULT ? 'CALLRESULT' : 'CALLERROR';
        this.logMessage(connection.id, connection.chargePointId, 'outgoing', messageTypeName, undefined, message);
      } catch (error) {
        console.error(`Failed to send message to ${connection.chargePointId}:`, error);
      }
    }
  }

  /**
   * Ping a specific connection
   */
  private pingConnection(connection: OCPPConnection): void {
    if (connection.socket.readyState === WebSocket.OPEN) {
      connection.socket.ping();
      this.connectionManager.markNotAlive(connection.id);
    }
  }

  /**
   * Start heartbeat interval
   */
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      this.connectionManager.getAllConnections().forEach(connection => {
        this.pingConnection(connection);
      });
    }, this.config.heartbeatInterval);
  }

  /**
   * Start cleanup interval
   */
  private startCleanup(): void {
    this.cleanupInterval = setInterval(() => {
      const removedCount = this.connectionManager.cleanup();
      if (removedCount > 0) {
        console.log(`Cleaned up ${removedCount} dead connections`);
      }
    }, this.config.connectionTimeout);
  }

  /**
   * Get server statistics
   */
  getStats() {
    return this.connectionManager.getStats();
  }

  /**
   * Get connection manager (for external access)
   */
  getConnectionManager(): ConnectionManager {
    return this.connectionManager;
  }

  /**
   * Stop the server
   */
  stop(): Promise<void> {
    return new Promise((resolve) => {
      if (this.heartbeatInterval) {
        clearInterval(this.heartbeatInterval);
      }
      if (this.cleanupInterval) {
        clearInterval(this.cleanupInterval);
      }

      this.wss.close(() => {
        console.log('OCPP WebSocket Server stopped');
        resolve();
      });
    });
  }
}
