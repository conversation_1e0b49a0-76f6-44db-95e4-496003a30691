import { WebSocketServer, WebSocket } from 'ws';
import { IncomingMessage } from 'http';
import { ConnectionManager } from './connection-manager';
import { ServerConfig, OCPPConnection } from './types/websocket';
import { OCPPMessageUnion, OCPPMessageType, OCPPErrorCode } from './types/ocpp';

export class OCPPWebSocketServer {
  private wss: WebSocketServer;
  private connectionManager: ConnectionManager;
  private config: ServerConfig;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private cleanupInterval: NodeJS.Timeout | null = null;

  constructor(config: Partial<ServerConfig> = {}) {
    this.config = {
      port: config.port || 8080,
      heartbeatInterval: config.heartbeatInterval || 30000, // 30 seconds
      connectionTimeout: config.connectionTimeout || 60000, // 60 seconds
      maxConnections: config.maxConnections || 1000
    };

    this.connectionManager = new ConnectionManager();
    this.wss = new WebSocketServer({
      port: this.config.port,
      verifyClient: this.verifyClient.bind(this)
    });

    this.setupEventHandlers();
    this.startHeartbeat();
    this.startCleanup();

    console.log(`OCPP WebSocket Server started on port ${this.config.port}`);
  }

  /**
   * Verify client connection (extract charge point ID from URL)
   */
  private verifyClient(info: { origin: string; secure: boolean; req: IncomingMessage }): boolean {
    const url = new URL(info.req.url || '', `http://${info.req.headers.host}`);
    const chargePointId = url.pathname.split('/').pop();
    
    if (!chargePointId || chargePointId.length === 0) {
      console.log('Connection rejected: No charge point ID in URL');
      return false;
    }

    // Check max connections
    if (this.connectionManager.getAllConnections().length >= this.config.maxConnections) {
      console.log(`Connection rejected: Max connections (${this.config.maxConnections}) reached`);
      return false;
    }

    return true;
  }

  /**
   * Setup WebSocket server event handlers
   */
  private setupEventHandlers(): void {
    this.wss.on('connection', (socket: WebSocket, request: IncomingMessage) => {
      this.handleConnection(socket, request);
    });

    this.wss.on('error', (error: Error) => {
      console.error('WebSocket Server Error:', error);
    });

    // Setup connection manager event listeners
    this.connectionManager.on('connect', (event) => {
      console.log(`Charge Point Connected: ${event.chargePointId} at ${event.timestamp.toISOString()}`);
    });

    this.connectionManager.on('disconnect', (event) => {
      console.log(`Charge Point Disconnected: ${event.chargePointId} at ${event.timestamp.toISOString()}`);
    });

    this.connectionManager.on('message', (event) => {
      console.log(`Message from ${event.chargePointId}:`, event.data);
    });

    this.connectionManager.on('error', (event) => {
      console.error(`Error from ${event.chargePointId}:`, event.data);
    });
  }

  /**
   * Handle new WebSocket connection
   */
  private handleConnection(socket: WebSocket, request: IncomingMessage): void {
    const url = new URL(request.url || '', `http://${request.headers.host}`);
    const chargePointId = url.pathname.split('/').pop() || 'unknown';
    const protocol = socket.protocol;

    // Add connection to manager
    const connection = this.connectionManager.addConnection(socket, chargePointId, protocol);

    // Setup socket event handlers
    socket.on('message', (data: Buffer) => {
      this.handleMessage(connection, data);
    });

    socket.on('close', (code: number, reason: Buffer) => {
      console.log(`Connection closed: ${chargePointId} (${code}: ${reason.toString()})`);
      this.connectionManager.removeConnection(connection.id);
    });

    socket.on('error', (error: Error) => {
      console.error(`Socket error for ${chargePointId}:`, error);
      this.connectionManager.removeConnection(connection.id);
    });

    socket.on('pong', () => {
      this.connectionManager.markAlive(connection.id);
    });

    // Send initial ping
    this.pingConnection(connection);
  }

  /**
   * Handle incoming message from charge point
   */
  private handleMessage(connection: OCPPConnection, data: Buffer): void {
    try {
      const messageStr = data.toString();
      const message: OCPPMessageUnion = JSON.parse(messageStr);

      // Validate message format
      if (!Array.isArray(message) || message.length < 3) {
        this.sendError(connection, '', OCPPErrorCode.FORMATION_VIOLATION, 'Invalid message format');
        return;
      }

      const [messageTypeId, messageId] = message;

      // Update connection activity
      this.connectionManager.updateLastSeen(connection.id);

      // Emit message event
      this.connectionManager['emitEvent']({
        type: 'message',
        connectionId: connection.id,
        chargePointId: connection.chargePointId,
        data: message,
        timestamp: new Date()
      });

      // Handle different message types
      switch (messageTypeId) {
        case OCPPMessageType.CALL:
          this.handleCall(connection, message as any);
          break;
        case OCPPMessageType.CALLRESULT:
          this.handleCallResult(connection, message as any);
          break;
        case OCPPMessageType.CALLERROR:
          this.handleCallError(connection, message as any);
          break;
        default:
          this.sendError(connection, messageId, OCPPErrorCode.NOT_SUPPORTED, 'Unsupported message type');
      }
    } catch (error) {
      console.error(`Error parsing message from ${connection.chargePointId}:`, error);
      this.sendError(connection, '', OCPPErrorCode.FORMATION_VIOLATION, 'Invalid JSON');
    }
  }

  /**
   * Handle CALL message (request from charge point)
   */
  private handleCall(connection: OCPPConnection, message: [number, string, string, any]): void {
    const [, messageId, action, payload] = message;
    
    console.log(`Received ${action} from ${connection.chargePointId}:`, payload);

    // For now, send a basic response for common actions
    switch (action) {
      case 'BootNotification':
        this.sendCallResult(connection, messageId, {
          status: 'Accepted',
          currentTime: new Date().toISOString(),
          interval: 300
        });
        break;
      case 'Heartbeat':
        this.sendCallResult(connection, messageId, {
          currentTime: new Date().toISOString()
        });
        break;
      case 'StatusNotification':
      case 'MeterValues':
      case 'StartTransaction':
      case 'StopTransaction':
        this.sendCallResult(connection, messageId, {});
        break;
      default:
        this.sendError(connection, messageId, OCPPErrorCode.NOT_IMPLEMENTED, `Action ${action} not implemented`);
    }
  }

  /**
   * Handle CALLRESULT message (response from charge point)
   */
  private handleCallResult(connection: OCPPConnection, message: [number, string, any]): void {
    const [, messageId, payload] = message;
    console.log(`Received CALLRESULT from ${connection.chargePointId} for message ${messageId}:`, payload);
  }

  /**
   * Handle CALLERROR message (error from charge point)
   */
  private handleCallError(connection: OCPPConnection, message: [number, string, string, string, any]): void {
    const [, messageId, errorCode, errorDescription, errorDetails] = message;
    console.error(`Received CALLERROR from ${connection.chargePointId} for message ${messageId}:`, {
      errorCode,
      errorDescription,
      errorDetails
    });
  }

  /**
   * Send CALLRESULT message to charge point
   */
  private sendCallResult(connection: OCPPConnection, messageId: string, payload: any): void {
    const message: [number, string, any] = [OCPPMessageType.CALLRESULT, messageId, payload];
    this.sendMessage(connection, message);
  }

  /**
   * Send CALLERROR message to charge point
   */
  private sendError(connection: OCPPConnection, messageId: string, errorCode: string, errorDescription: string, errorDetails?: any): void {
    const message: [number, string, string, string, any] = [
      OCPPMessageType.CALLERROR,
      messageId,
      errorCode,
      errorDescription,
      errorDetails || {}
    ];
    this.sendMessage(connection, message);
  }

  /**
   * Send message to charge point
   */
  private sendMessage(connection: OCPPConnection, message: any): void {
    if (connection.socket.readyState === WebSocket.OPEN) {
      try {
        connection.socket.send(JSON.stringify(message));
        this.connectionManager.updateLastSeen(connection.id);
      } catch (error) {
        console.error(`Failed to send message to ${connection.chargePointId}:`, error);
      }
    }
  }

  /**
   * Ping a specific connection
   */
  private pingConnection(connection: OCPPConnection): void {
    if (connection.socket.readyState === WebSocket.OPEN) {
      connection.socket.ping();
      this.connectionManager.markNotAlive(connection.id);
    }
  }

  /**
   * Start heartbeat interval
   */
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      this.connectionManager.getAllConnections().forEach(connection => {
        this.pingConnection(connection);
      });
    }, this.config.heartbeatInterval);
  }

  /**
   * Start cleanup interval
   */
  private startCleanup(): void {
    this.cleanupInterval = setInterval(() => {
      const removedCount = this.connectionManager.cleanup();
      if (removedCount > 0) {
        console.log(`Cleaned up ${removedCount} dead connections`);
      }
    }, this.config.connectionTimeout);
  }

  /**
   * Get server statistics
   */
  getStats() {
    return this.connectionManager.getStats();
  }

  /**
   * Get connection manager (for external access)
   */
  getConnectionManager(): ConnectionManager {
    return this.connectionManager;
  }

  /**
   * Stop the server
   */
  stop(): Promise<void> {
    return new Promise((resolve) => {
      if (this.heartbeatInterval) {
        clearInterval(this.heartbeatInterval);
      }
      if (this.cleanupInterval) {
        clearInterval(this.cleanupInterval);
      }

      this.wss.close(() => {
        console.log('OCPP WebSocket Server stopped');
        resolve();
      });
    });
  }
}
