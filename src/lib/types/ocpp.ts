// OCPP 1.6 Message Types
export enum OCPPMessageType {
  CALL = 2,
  CALLRESULT = 3,
  CALLERROR = 4
}

// OCPP 1.6 Actions (from Charge Point to Central System)
export enum OCPPAction {
  // Core Profile
  AUTHORIZE = 'Authorize',
  BOOT_NOTIFICATION = 'BootNotification',
  DATA_TRANSFER = 'DataTransfer',
  HEARTBEAT = 'Heartbeat',
  METER_VALUES = 'MeterValues',
  START_TRANSACTION = 'StartTransaction',
  STATUS_NOTIFICATION = 'StatusNotification',
  STOP_TRANSACTION = 'StopTransaction',

  // Firmware Management Profile
  DIAGNOSTICS_STATUS_NOTIFICATION = 'DiagnosticsStatusNotification',
  FIRMWARE_STATUS_NOTIFICATION = 'FirmwareStatusNotification',
}

// OCPP 1.6 Actions (from Central System to Charge Point)
export enum OCPPRemoteAction {
  // Core Profile
  CANCEL_RESERVATION = 'CancelReservation',
  CHANGE_AVAILABILITY = 'ChangeAvailability',
  CHANGE_CONFIGURATION = 'ChangeConfiguration',
  CLEAR_CACHE = 'ClearCache',
  DATA_TRANSFER = 'DataTransfer',
  GET_CONFIGURATION = 'GetConfiguration',
  REMOTE_START_TRANSACTION = 'RemoteStartTransaction',
  REMOTE_STOP_TRANSACTION = 'RemoteStopTransaction',
  RESERVE_NOW = 'ReserveNow',
  RESET = 'Reset',
  UNLOCK_CONNECTOR = 'UnlockConnector',

  // Smart Charging Profile
  CLEAR_CHARGING_PROFILE = 'ClearChargingProfile',
  GET_COMPOSITE_SCHEDULE = 'GetCompositeSchedule',
  SET_CHARGING_PROFILE = 'SetChargingProfile',

  // Firmware Management Profile
  GET_DIAGNOSTICS = 'GetDiagnostics',
  UPDATE_FIRMWARE = 'UpdateFirmware',

  // Local Auth List Management Profile
  GET_LOCAL_LIST_VERSION = 'GetLocalListVersion',
  SEND_LOCAL_LIST = 'SendLocalList',

  // Reservation Profile
  TRIGGER_MESSAGE = 'TriggerMessage',
}

// ===== OCPP 1.6 Request/Response Types (based on JSON schemas) =====

// BootNotification (from schema)
export interface BootNotificationRequest {
  chargePointVendor: string; // maxLength: 20
  chargePointModel: string; // maxLength: 20
  chargePointSerialNumber?: string; // maxLength: 25
  chargeBoxSerialNumber?: string; // maxLength: 25
  firmwareVersion?: string; // maxLength: 50
  iccid?: string; // maxLength: 20
  imsi?: string; // maxLength: 20
  meterType?: string; // maxLength: 25
  meterSerialNumber?: string; // maxLength: 25
}

export interface BootNotificationResponse {
  status: 'Accepted' | 'Pending' | 'Rejected';
  currentTime: string; // ISO 8601 date-time
  interval: number;
}

// Heartbeat (from schema)
export interface HeartbeatRequest {
  // Empty object
}

export interface HeartbeatResponse {
  currentTime: string; // ISO 8601 date-time
}

// StatusNotification (from schema)
export interface StatusNotificationRequest {
  connectorId: number;
  errorCode: 'ConnectorLockFailure' | 'EVCommunicationError' | 'GroundFailure' |
            'HighTemperature' | 'InternalError' | 'LocalListConflict' | 'NoError' |
            'OtherError' | 'OverCurrentFailure' | 'PowerMeterFailure' |
            'PowerSwitchFailure' | 'ReaderFailure' | 'ResetFailure' |
            'UnderVoltage' | 'OverVoltage' | 'WeakSignal';
  status: 'Available' | 'Preparing' | 'Charging' | 'SuspendedEVSE' | 'SuspendedEV' |
          'Finishing' | 'Reserved' | 'Unavailable' | 'Faulted';
  info?: string; // maxLength: 50
  timestamp?: string; // ISO 8601 date-time
  vendorId?: string; // maxLength: 255
  vendorErrorCode?: string; // maxLength: 50
}

export interface StatusNotificationResponse {
  // Empty object
}

// StartTransaction (from schema)
export interface StartTransactionRequest {
  connectorId: number;
  idTag: string; // maxLength: 20
  meterStart: number;
  timestamp: string; // ISO 8601 date-time
  reservationId?: number;
}

export interface OCPPCallMessage {
  messageTypeId: OCPPMessageType.CALL;
  messageId: string;
  action: string;
  payload: any;
}

export interface OCPPCallResultMessage {
  messageTypeId: OCPPMessageType.CALLRESULT;
  messageId: string;
  payload: any;
}

export interface OCPPCallErrorMessage {
  messageTypeId: OCPPMessageType.CALLERROR;
  messageId: string;
  errorCode: string;
  errorDescription: string;
  errorDetails?: any;
}

export type OCPPMessageUnion = OCPPCallMessage | OCPPCallResultMessage | OCPPCallErrorMessage;

// Common OCPP Error Codes
export enum OCPPErrorCode {
  NOT_IMPLEMENTED = 'NotImplemented',
  NOT_SUPPORTED = 'NotSupported',
  INTERNAL_ERROR = 'InternalError',
  PROTOCOL_ERROR = 'ProtocolError',
  SECURITY_ERROR = 'SecurityError',
  FORMATION_VIOLATION = 'FormationViolation',
  PROPERTY_CONSTRAINT_VIOLATION = 'PropertyConstraintViolation',
  OCCURRENCE_CONSTRAINT_VIOLATION = 'OccurrenceConstraintViolation',
  TYPE_CONSTRAINT_VIOLATION = 'TypeConstraintViolation',
  GENERIC_ERROR = 'GenericError'
}

// Boot Notification Status
export enum BootNotificationStatus {
  ACCEPTED = 'Accepted',
  PENDING = 'Pending',
  REJECTED = 'Rejected'
}

// Charge Point Status
export enum ChargePointStatus {
  AVAILABLE = 'Available',
  PREPARING = 'Preparing',
  CHARGING = 'Charging',
  SUSPENDED_EVSE = 'SuspendedEVSE',
  SUSPENDED_EV = 'SuspendedEV',
  FINISHING = 'Finishing',
  RESERVED = 'Reserved',
  UNAVAILABLE = 'Unavailable',
  FAULTED = 'Faulted'
}
