import { WebSocket } from 'ws';

export interface OCPPConnection {
  id: string;
  chargePointId: string;
  socket: WebSocket;
  isAlive: boolean;
  connectedAt: Date;
  lastSeen: Date;
  protocol?: string;
}

export interface OCPPMessage {
  messageTypeId: number;
  messageId: string;
  action?: string;
  payload: any;
}

export interface ConnectionStats {
  totalConnections: number;
  activeConnections: number;
  connectionsByProtocol: Record<string, number>;
}

export interface ServerConfig {
  port: number;
  heartbeatInterval: number;
  connectionTimeout: number;
  maxConnections: number;
}

export type ConnectionEventType = 'connect' | 'disconnect' | 'message' | 'error' | 'heartbeat';

export interface ConnectionEvent {
  type: ConnectionEventType;
  connectionId: string;
  chargePointId?: string;
  data?: any;
  timestamp: Date;
}
