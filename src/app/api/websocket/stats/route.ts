import { NextRequest } from 'next/server';

// This would need to be connected to a running server instance
// For now, return mock data
export async function GET(request: NextRequest) {
  return new Response(
    JSON.stringify({
      message: 'Server statistics endpoint',
      note: 'This endpoint would show real-time statistics when connected to a running WebSocket server',
      mockStats: {
        totalConnections: 0,
        activeConnections: 0,
        connectionsByProtocol: {},
        timestamp: new Date().toISOString()
      }
    }),
    {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );
}
