import { NextRequest } from 'next/server';

export async function GET(request: NextRequest) {
  return new Response(
    JSON.stringify({
      message: 'OCPP WebSocket Server Info',
      endpoints: {
        websocket: 'ws://localhost:8080/{chargePointId}',
        stats: '/api/websocket/stats'
      },
      note: 'Use the standalone server (npm run server) for WebSocket connections'
    }),
    {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );
}
