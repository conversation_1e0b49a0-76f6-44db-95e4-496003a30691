{"name": "ocpp_backend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build && tsc --project tsconfig.server.json", "start": "next start", "lint": "next lint", "server": "npm run build:server && node server.js", "build:server": "tsc --project tsconfig.server.json", "dev:server": "tsc --project tsconfig.server.json --watch & nodemon server.js", "test:client": "node test-client.js", "monitor": "node monitor.js"}, "dependencies": {"@types/uuid": "^10.0.0", "@types/ws": "^8.18.1", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "uuid": "^11.1.0", "ws": "^8.18.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "nodemon": "^3.1.10", "tailwindcss": "^4", "typescript": "^5"}}