# OCPP Backend

Ein OCPP (Open Charge Point Protocol) Backend-Server für die Verwaltung von Elektrofahrzeug-Ladestationen.

## Features

- **WebSocket-Server**: Verwaltet viele gleichzeitige WebSocket-Verbindungen
- **OCPP 1.6 Unterstützung**: Implementiert das OCPP 1.6 Protokoll
- **Verbindungsmanagement**: Automatische Überwachung und Verwaltung von Verbindungen
- **Heartbeat-Monitoring**: Überwacht die Verbindungsqualität
- **Skalierbar**: Designed für viele gleichzeitige Verbindungen

## Erste Schritte

### 1. Dependencies installieren

```bash
npm install
```

### 2. WebSocket-Server starten

```bash
# Server kompilieren und starten
npm run server

# Oder für Entwicklung mit Auto-Reload
npm run dev:server
```

Der WebSocket-Server läuft standardmäßig auf Port 8080.

### 3. Ladestationen verbinden

Ladestationen können sich über WebSocket verbinden:

```
ws://localhost:8080/{chargePointId}
```

Beispiel: `ws://localhost:8080/CP001`

## Verfügbare Skripte

- `npm run server` - Kompiliert und startet den WebSocket-Server
- `npm run build:server` - Kompiliert nur den Server-Code
- `npm run dev:server` - Startet den Server im Entwicklungsmodus
- `npm run dev` - Startet die Next.js Entwicklungsumgebung
- `npm run build` - Baut das komplette Projekt

## Projektstruktur

```
src/
├── lib/
│   ├── types/
│   │   ├── websocket.ts    # WebSocket-Typen
│   │   └── ocpp.ts         # OCPP-Protokoll-Typen
│   ├── connection-manager.ts # Verbindungsmanagement
│   └── websocket-server.ts  # Haupt-WebSocket-Server
├── app/
│   └── api/websocket/      # Next.js API Routes
server.js                   # Standalone-Server
ocpp_1_6_schema/           # OCPP 1.6 JSON-Schemas
```

## OCPP-Nachrichten

Der Server unterstützt grundlegende OCPP 1.6 Nachrichten:

- **BootNotification**: Registrierung der Ladestation
- **Heartbeat**: Verbindungsüberwachung
- **StatusNotification**: Status-Updates
- **MeterValues**: Messwerte
- **StartTransaction**: Ladevorgang starten
- **StopTransaction**: Ladevorgang beenden

## Konfiguration

Der Server kann über Umgebungsvariablen konfiguriert werden:

- `OCPP_PORT`: WebSocket-Server Port (Standard: 8080)

## Nächste Schritte

Dieses ist der erste Schritt des OCPP-Backends. Geplante Erweiterungen:

1. ✅ WebSocket-Server mit Verbindungsmanagement
2. 🔄 Vollständige OCPP 1.6 Implementierung
3. 🔄 Datenbank-Integration
4. 🔄 REST API für Management
5. 🔄 Web-UI für Monitoring
6. 🔄 Authentifizierung und Autorisierung
7. 🔄 Load Balancing und Clustering
