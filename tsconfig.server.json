{"extends": "./tsconfig.json", "compilerOptions": {"module": "CommonJS", "target": "ES2020", "outDir": "./dist", "rootDir": "./src", "declaration": false, "sourceMap": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true}, "include": ["src/lib/**/*"], "exclude": ["src/app/**/*", "node_modules", "dist"]}