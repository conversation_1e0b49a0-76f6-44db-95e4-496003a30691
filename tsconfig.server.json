{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "declaration": false, "sourceMap": true, "removeComments": false, "noImplicitAny": false}, "include": ["src/lib/**/*"], "exclude": ["src/app/**/*", "node_modules", "dist"]}