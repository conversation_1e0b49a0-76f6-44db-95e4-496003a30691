{"$schema": "http://json-schema.org/draft-04/schema#", "id": "urn:OCPP:1.6:2019:12:TriggerMessageRequest", "title": "TriggerMessageRequest", "type": "object", "properties": {"requestedMessage": {"type": "string", "additionalProperties": false, "enum": ["BootNotification", "DiagnosticsStatusNotification", "FirmwareStatusNotification", "Heartbeat", "<PERSON>er<PERSON><PERSON><PERSON>", "StatusNotification"]}, "connectorId": {"type": "integer"}}, "additionalProperties": false, "required": ["requestedMessage"]}