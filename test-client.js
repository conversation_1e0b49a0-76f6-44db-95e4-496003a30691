#!/usr/bin/env node

const WebSocket = require('ws');

// Test client to simulate a charge point
class TestChargePoint {
  constructor(chargePointId, serverUrl = 'ws://localhost:8080') {
    this.chargePointId = chargePointId;
    this.serverUrl = `${serverUrl}/${chargePointId}`;
    this.ws = null;
    this.messageId = 1;
  }

  connect() {
    console.log(`Connecting charge point ${this.chargePointId} to ${this.serverUrl}`);
    
    this.ws = new WebSocket(this.serverUrl, 'ocpp1.6');

    this.ws.on('open', () => {
      console.log(`✅ Connected: ${this.chargePointId}`);
      this.sendBootNotification();
    });

    this.ws.on('message', (data) => {
      try {
        const message = JSON.parse(data.toString());
        console.log(`📨 Received from server:`, message);
        this.handleMessage(message);
      } catch (error) {
        console.error(`❌ Error parsing message:`, error);
      }
    });

    this.ws.on('close', (code, reason) => {
      console.log(`🔌 Disconnected: ${this.chargePointId} (${code}: ${reason})`);
    });

    this.ws.on('error', (error) => {
      console.error(`❌ WebSocket error for ${this.chargePointId}:`, error);
    });

    this.ws.on('ping', () => {
      console.log(`🏓 Ping received from server`);
    });
  }

  sendMessage(action, payload) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      const message = [2, this.messageId.toString(), action, payload];
      console.log(`📤 Sending ${action}:`, payload);
      this.ws.send(JSON.stringify(message));
      this.messageId++;
    }
  }

  sendBootNotification() {
    this.sendMessage('BootNotification', {
      chargePointVendor: 'TestVendor',
      chargePointModel: 'TestModel',
      chargePointSerialNumber: `SN-${this.chargePointId}`,
      firmwareVersion: '1.0.0',
      chargeBoxSerialNumber: `CB-${this.chargePointId}`,
      meterType: 'TestMeter',
      meterSerialNumber: `MT-${this.chargePointId}`
    });
  }

  sendHeartbeat() {
    this.sendMessage('Heartbeat', {});
  }

  sendStatusNotification(connectorId = 1, status = 'Available', errorCode = 'NoError') {
    this.sendMessage('StatusNotification', {
      connectorId,
      status,
      errorCode,
      timestamp: new Date().toISOString()
    });
  }

  sendStartTransaction(connectorId = 1, idTag = 'TEST_TAG_001') {
    this.sendMessage('StartTransaction', {
      connectorId,
      idTag,
      meterStart: 0,
      timestamp: new Date().toISOString()
    });
  }

  sendStopTransaction(transactionId, meterStop = 1000) {
    this.sendMessage('StopTransaction', {
      transactionId,
      timestamp: new Date().toISOString(),
      meterStop,
      reason: 'Local'
    });
  }

  sendMeterValues(connectorId = 1, transactionId) {
    this.sendMessage('MeterValues', {
      connectorId,
      transactionId,
      meterValue: [{
        timestamp: new Date().toISOString(),
        sampledValue: [{
          value: '1234',
          measurand: 'Energy.Active.Import.Register',
          unit: 'Wh'
        }]
      }]
    });
  }

  handleMessage(message) {
    const [messageTypeId, messageId, ...rest] = message;
    
    switch (messageTypeId) {
      case 3: // CALLRESULT
        console.log(`✅ Call result for message ${messageId}:`, rest[0]);
        break;
      case 4: // CALLERROR
        console.log(`❌ Call error for message ${messageId}:`, rest);
        break;
      default:
        console.log(`❓ Unknown message type: ${messageTypeId}`);
    }
  }

  startHeartbeat(interval = 30000) {
    setInterval(() => {
      this.sendHeartbeat();
    }, interval);
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
    }
  }
}

// Main execution
if (require.main === module) {
  const chargePointId = process.argv[2] || 'CP001';
  const serverUrl = process.argv[3] || 'ws://localhost:8080';

  console.log('🚗 Starting OCPP Test Client');
  console.log(`Charge Point ID: ${chargePointId}`);
  console.log(`Server URL: ${serverUrl}`);
  console.log('Press Ctrl+C to stop\n');

  const chargePoint = new TestChargePoint(chargePointId, serverUrl);
  chargePoint.connect();

  // Start heartbeat after 5 seconds
  setTimeout(() => {
    chargePoint.startHeartbeat(10000); // Every 10 seconds
  }, 5000);

  // Send status notification after 3 seconds
  setTimeout(() => {
    chargePoint.sendStatusNotification();
  }, 3000);

  // Simulate a charging session after 8 seconds
  setTimeout(() => {
    console.log('🔌 Starting charging simulation...');
    chargePoint.sendStartTransaction();
  }, 8000);

  // Send meter values after 12 seconds
  setTimeout(() => {
    chargePoint.sendMeterValues(1, 123456); // Using a mock transaction ID
  }, 12000);

  // Stop transaction after 16 seconds
  setTimeout(() => {
    console.log('🛑 Stopping charging simulation...');
    chargePoint.sendStopTransaction(123456, 5000); // Mock transaction ID and meter stop
  }, 16000);

  // Graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down test client...');
    chargePoint.disconnect();
    process.exit(0);
  });
}

module.exports = TestChargePoint;
