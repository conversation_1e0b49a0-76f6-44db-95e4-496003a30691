#!/usr/bin/env node

const WebSocket = require('ws');

// Simple monitoring script to check server status
class ServerMonitor {
  constructor(serverHost = 'localhost', serverPort = 8080) {
    this.serverHost = serverHost;
    this.serverPort = serverPort;
  }

  async checkWebSocketServer() {
    return new Promise((resolve) => {
      const ws = new WebSocket(`ws://${this.serverHost}:${this.serverPort}/monitor-test`);
      
      const timeout = setTimeout(() => {
        ws.close();
        resolve({ status: 'timeout', message: 'Connection timeout' });
      }, 5000);

      ws.on('open', () => {
        clearTimeout(timeout);
        ws.close();
        resolve({ status: 'ok', message: 'WebSocket server is running' });
      });

      ws.on('error', (error) => {
        clearTimeout(timeout);
        resolve({ status: 'error', message: error.message });
      });
    });
  }

  async monitor() {
    console.log('🔍 OCPP Server Monitor');
    console.log('='.repeat(50));
    
    const wsCheck = await this.checkWebSocketServer();
    console.log(`WebSocket Server: ${wsCheck.status === 'ok' ? '✅' : '❌'} ${wsCheck.message}`);
    
    console.log(`Timestamp: ${new Date().toISOString()}`);
    console.log('='.repeat(50));
  }

  startContinuousMonitoring(interval = 30000) {
    console.log(`🔄 Starting continuous monitoring (every ${interval/1000}s)`);
    console.log('Press Ctrl+C to stop\n');
    
    this.monitor();
    const intervalId = setInterval(() => {
      this.monitor();
    }, interval);

    process.on('SIGINT', () => {
      console.log('\n🛑 Stopping monitor...');
      clearInterval(intervalId);
      process.exit(0);
    });
  }
}

// Main execution
if (require.main === module) {
  const monitor = new ServerMonitor();
  
  const command = process.argv[2];
  
  if (command === 'continuous') {
    monitor.startContinuousMonitoring();
  } else {
    monitor.monitor().then(() => {
      console.log('\nUse "node monitor.js continuous" for continuous monitoring');
    });
  }
}

module.exports = ServerMonitor;
